# Cloudflare Pages 部署指南

## 方法一：通过 Git 仓库部署（推荐）

### 1. 准备 Git 仓库
```bash
# 初始化 Git 仓库
git init

# 添加所有文件
git add .

# 提交代码
git commit -m "Initial commit: 邮箱批量生成器"

# 添加远程仓库（GitHub/GitLab/Bitbucket）
git remote add origin <你的仓库地址>

# 推送到远程仓库
git push -u origin main
```

### 2. 在 Cloudflare Pages 中部署
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 进入 **Pages** 部分
3. 点击 **创建项目**
4. 选择 **连接到 Git**
5. 选择你的 Git 提供商（GitHub/GitLab/Bitbucket）
6. 授权 Cloudflare 访问你的仓库
7. 选择包含项目的仓库
8. 配置构建设置：
   - **项目名称**: `email-batch-generator`（或你喜欢的名称）
   - **生产分支**: `main`
   - **构建命令**: 留空
   - **构建输出目录**: `/`
9. 点击 **保存并部署**

### 3. 等待部署完成
- 部署通常需要 1-3 分钟
- 完成后会获得一个 `.pages.dev` 域名
- 可以在自定义域名中绑定你自己的域名

## 方法二：直接上传文件

### 1. 创建项目
1. 登录 Cloudflare Dashboard
2. 进入 Pages 部分
3. 点击 **上传资产**
4. 输入项目名称

### 2. 上传文件
1. 将以下文件打包成 ZIP：
   - `index.html`
   - `style.css`
   - `script.js`
   - `_headers`
2. 上传 ZIP 文件
3. 点击 **部署站点**

## 自定义域名配置

### 1. 添加自定义域名
1. 在项目设置中点击 **自定义域名**
2. 点击 **设置自定义域名**
3. 输入你的域名
4. 按照提示配置 DNS 记录

### 2. SSL 证书
- Cloudflare 会自动为你的域名提供免费 SSL 证书
- 通常在几分钟内生效

## 环境变量配置（可选）

如果需要在不同环境使用不同的 API 配置：

1. 在项目设置中找到 **环境变量**
2. 添加以下变量：
   - `API_BASE_URL`: API 基础地址
   - `API_TOKEN`: API 令牌
   - `EMAIL_DOMAIN`: 邮箱域名

然后修改 `script.js` 中的配置：
```javascript
const API_CONFIG = {
    baseUrl: process.env.API_BASE_URL || 'https://eoceshi.552500.xy',
    token: process.env.API_TOKEN || 'apiceshi******',
    emailDomain: process.env.EMAIL_DOMAIN || 'ai999.dpdns.org',
    defaultPassword: 'Ai12345@'
};
```

## 性能优化建议

### 1. 启用缓存
在 `_headers` 文件中已配置了基本的安全头，可以添加缓存配置：
```
/*.css
  Cache-Control: public, max-age=31536000

/*.js
  Cache-Control: public, max-age=31536000

/*.html
  Cache-Control: public, max-age=3600
```

### 2. 启用压缩
Cloudflare 默认启用 Gzip 压缩，无需额外配置。

## 监控和分析

### 1. 启用 Web Analytics
1. 在项目设置中找到 **Web Analytics**
2. 启用分析功能
3. 可以查看访问统计、性能指标等

### 2. 查看部署日志
- 在项目的 **部署** 标签中可以查看每次部署的日志
- 有助于排查部署问题

## 故障排除

### 常见问题

1. **CORS 错误**
   - 确保 API 服务器允许来自你域名的跨域请求
   - 检查 `_headers` 文件中的 CSP 配置

2. **API 调用失败**
   - 检查 API 地址是否正确
   - 确认 Authorization Token 是否有效
   - 查看浏览器开发者工具的网络标签

3. **页面无法访问**
   - 检查 DNS 配置是否正确
   - 确认域名解析是否生效

### 调试技巧

1. 使用浏览器开发者工具查看控制台错误
2. 检查网络请求的状态码和响应
3. 在 Cloudflare Dashboard 中查看部署日志

## 更新部署

### Git 方式
```bash
# 修改代码后
git add .
git commit -m "更新描述"
git push

# Cloudflare Pages 会自动重新部署
```

### 直接上传方式
1. 重新打包文件
2. 在项目中点击 **管理** → **上传新版本**
3. 上传新的 ZIP 文件

## 安全建议

1. **定期更新 API Token**
2. **监控访问日志**
3. **设置适当的 CSP 策略**
4. **使用 HTTPS**（Cloudflare 默认启用）

部署完成后，你的邮箱批量生成器就可以通过 Cloudflare Pages 提供的域名访问了！
