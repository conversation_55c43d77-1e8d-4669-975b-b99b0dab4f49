# 邮箱批量生成器

一个用于批量创建邮箱账户的前端网页应用，部署在 Cloudflare Pages 上。

## 功能特性

- 🚀 批量生成邮箱账户
- 🔑 自动生成访问令牌（7天有效期）
- 📊 实时进度显示
- 📋 成功/失败结果分类显示
- 💾 支持导出结果到文件
- 📱 响应式设计，支持移动端
- 🔒 安全的 API 调用

## 使用说明

### 第一步：配置管理员账户
1. **管理员邮箱**: 输入有权限的管理员邮箱
2. **管理员密码**: 输入管理员账户密码

### 第二步：设置生成参数
1. **邮箱前缀**: 输入邮箱的前缀部分（如：user）
2. **起始编号**: 设置编号的起始值（如：1）
3. **生成数量**: 设置要生成的邮箱数量（1-100）
4. **权限身份名**: 可选，留空使用默认权限

### 第三步：开始生成
- 点击"开始生成"按钮
- 系统会自动生成新的访问令牌（7天有效期）
- 然后批量创建邮箱账户

生成的邮箱格式：`{前缀}{编号}@ai999.dpdns.org`

例如：<EMAIL>, <EMAIL> ...

## 技术栈

- 纯 HTML/CSS/JavaScript
- Font Awesome 图标
- 现代 CSS Grid 布局
- Fetch API 进行网络请求

## API 配置

项目使用以下 API 配置：
- 后端服务: `https://eoceshi.552500.xy`
- 邮箱域名: `ai999.dpdns.org`
- 默认密码: `Ai12345@`
- 访问令牌: 动态生成（每次使用前自动获取新令牌）

### Token 管理
- 每次生成邮箱前会自动调用 `/api/public/genToken` 获取新令牌
- 令牌有效期为 7 天
- 新令牌生成后，旧令牌会自动失效

## 部署到 Cloudflare Pages

1. 将项目文件上传到 Git 仓库
2. 在 Cloudflare Pages 中连接仓库
3. 设置构建配置：
   - 构建命令: 留空（静态文件）
   - 构建输出目录: `/`
4. 部署完成

## 文件结构

```
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # JavaScript 逻辑
├── _headers            # Cloudflare Pages 安全头配置
└── README.md           # 项目说明
```

## 安全特性

- CSP (Content Security Policy) 配置
- XSS 保护
- 安全的 HTTP 头设置
- API Token 保护

## 浏览器兼容性

支持所有现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License
