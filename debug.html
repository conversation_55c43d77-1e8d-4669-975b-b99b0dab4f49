<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面 - 邮箱批量生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .debug-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .console-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>邮箱批量生成器 - 调试页面</h1>
    
    <div class="debug-section">
        <h2>🔧 配置信息</h2>
        <p><strong>API 地址:</strong> https://eoceshi.552500.xy</p>
        <p><strong>邮箱域名:</strong> ai999.dpdns.org</p>
        <p><strong>默认密码:</strong> Ai12345@</p>
        <p><strong>当前 Token:</strong> <span id="currentToken">未生成</span></p>
    </div>
    
    <div class="debug-section">
        <h2>📝 管理员账户测试</h2>
        <input type="email" id="adminEmail" placeholder="管理员邮箱" value="<EMAIL>">
        <input type="password" id="adminPassword" placeholder="管理员密码" value="123456">
        <br>
        <button class="debug-button" onclick="testTokenGeneration()">1. 测试 Token 生成</button>
        <button class="debug-button" onclick="testAPIConnection()">2. 测试 API 连接</button>
        <div id="tokenResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>📧 单个邮箱测试</h2>
        <input type="text" id="testEmailPrefix" placeholder="邮箱前缀" value="debug">
        <button class="debug-button" onclick="testSingleEmailCreation()">3. 创建单个邮箱</button>
        <div id="singleEmailResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>📊 批量测试（小规模）</h2>
        <input type="text" id="batchPrefix" placeholder="批量前缀" value="batch">
        <input type="number" id="batchCount" value="2" min="1" max="3">
        <button class="debug-button" onclick="testBatchCreation()">4. 批量创建测试</button>
        <div id="batchResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>🔍 网络检查</h2>
        <button class="debug-button" onclick="testNetworkConnectivity()">5. 测试网络连接</button>
        <button class="debug-button" onclick="testCORS()">6. 测试 CORS 设置</button>
        <div id="networkResult" class="debug-result"></div>
    </div>
    
    <div class="debug-section">
        <h2>📋 控制台日志</h2>
        <button class="debug-button" onclick="clearConsole()">清空日志</button>
        <div id="consoleLog" class="console-log"></div>
    </div>

    <script>
        // API 配置
        const API_CONFIG = {
            baseUrl: 'https://eoceshi.552500.xy',
            token: null,
            emailDomain: 'ai999.dpdns.org',
            defaultPassword: 'Ai12345@'
        };

        // 劫持 console.log 显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleDiv = document.getElementById('consoleLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleDiv.textContent += logEntry;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };

        function clearConsole() {
            document.getElementById('consoleLog').textContent = '';
        }

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `debug-result ${type}`;
            element.textContent = message;
        }

        function updateTokenDisplay() {
            const tokenSpan = document.getElementById('currentToken');
            if (API_CONFIG.token) {
                tokenSpan.textContent = API_CONFIG.token.substring(0, 16) + '...';
            } else {
                tokenSpan.textContent = '未生成';
            }
        }

        // 1. 测试 Token 生成
        async function testTokenGeneration() {
            const adminEmail = document.getElementById('adminEmail').value.trim();
            const adminPassword = document.getElementById('adminPassword').value.trim();
            
            console.log('开始测试 Token 生成');
            console.log('管理员邮箱:', adminEmail);
            console.log('管理员密码:', adminPassword ? '已设置' : '未设置');
            
            if (!adminEmail || !adminPassword) {
                showResult('tokenResult', '请输入管理员邮箱和密码', 'error');
                return;
            }
            
            showResult('tokenResult', '正在生成 Token...', 'info');
            
            try {
                const requestBody = {
                    email: adminEmail,
                    password: adminPassword
                };
                
                console.log('Token 请求体:', JSON.stringify(requestBody));
                
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/genToken`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                console.log('Token 响应状态:', response.status, response.statusText);
                console.log('Token 响应头:', [...response.headers.entries()]);
                
                const data = await response.json();
                console.log('Token 响应数据:', JSON.stringify(data, null, 2));
                
                if (response.ok && data.code === 200) {
                    API_CONFIG.token = data.data.token;
                    updateTokenDisplay();
                    showResult('tokenResult', `✓ Token 生成成功！\nToken: ${data.data.token}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('tokenResult', `✗ Token 生成失败\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                console.error('Token 生成异常:', error);
                showResult('tokenResult', `✗ 请求失败: ${error.message}\n\n错误详情: ${error.stack}`, 'error');
            }
        }

        // 2. 测试 API 连接
        async function testAPIConnection() {
            if (!API_CONFIG.token) {
                showResult('tokenResult', '请先生成 Token', 'warning');
                return;
            }
            
            console.log('开始测试 API 连接');
            console.log('使用 Token:', API_CONFIG.token.substring(0, 8) + '...');
            
            showResult('tokenResult', '正在测试 API 连接...', 'info');
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.token
                    },
                    body: JSON.stringify({
                        list: []
                    })
                });
                
                console.log('API 连接响应状态:', response.status, response.statusText);
                
                const data = await response.json();
                console.log('API 连接响应数据:', JSON.stringify(data, null, 2));
                
                if (response.ok) {
                    showResult('tokenResult', `✓ API 连接成功！\n状态码: ${response.status}\n\n响应:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('tokenResult', `⚠ API 响应异常\n状态码: ${response.status}\n\n响应:\n${JSON.stringify(data, null, 2)}`, 'warning');
                }
            } catch (error) {
                console.error('API 连接异常:', error);
                showResult('tokenResult', `✗ 连接失败: ${error.message}`, 'error');
            }
        }

        // 3. 创建单个邮箱
        async function testSingleEmailCreation() {
            if (!API_CONFIG.token) {
                showResult('singleEmailResult', '请先生成 Token', 'warning');
                return;
            }
            
            const prefix = document.getElementById('testEmailPrefix').value.trim();
            if (!prefix) {
                showResult('singleEmailResult', '请输入邮箱前缀', 'error');
                return;
            }
            
            const email = `${prefix}_${Date.now()}@${API_CONFIG.emailDomain}`;
            console.log('开始创建单个邮箱:', email);
            
            showResult('singleEmailResult', `正在创建: ${email}`, 'info');
            
            try {
                const userList = [{
                    email: email,
                    password: API_CONFIG.defaultPassword
                }];
                
                console.log('创建邮箱请求体:', JSON.stringify({ list: userList }));
                
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.token
                    },
                    body: JSON.stringify({
                        list: userList
                    })
                });
                
                console.log('创建邮箱响应状态:', response.status, response.statusText);
                
                const data = await response.json();
                console.log('创建邮箱响应数据:', JSON.stringify(data, null, 2));
                
                if (response.ok && data.code === 200) {
                    showResult('singleEmailResult', `✓ 邮箱创建成功！\n邮箱: ${email}\n密码: ${API_CONFIG.defaultPassword}\n\n响应:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('singleEmailResult', `✗ 创建失败\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                console.error('创建邮箱异常:', error);
                showResult('singleEmailResult', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 4. 批量创建测试
        async function testBatchCreation() {
            if (!API_CONFIG.token) {
                showResult('batchResult', '请先生成 Token', 'warning');
                return;
            }
            
            const prefix = document.getElementById('batchPrefix').value.trim();
            const count = parseInt(document.getElementById('batchCount').value);
            
            if (!prefix) {
                showResult('batchResult', '请输入批量前缀', 'error');
                return;
            }
            
            if (count < 1 || count > 3) {
                showResult('batchResult', '测试数量应在 1-3 之间', 'error');
                return;
            }
            
            console.log(`开始批量创建 ${count} 个邮箱，前缀: ${prefix}`);
            
            const timestamp = Date.now();
            const emails = [];
            
            for (let i = 1; i <= count; i++) {
                emails.push({
                    email: `${prefix}${i}_${timestamp}@${API_CONFIG.emailDomain}`,
                    password: API_CONFIG.defaultPassword
                });
            }
            
            console.log('批量创建邮箱列表:', emails);
            
            showResult('batchResult', `正在批量创建 ${count} 个邮箱...`, 'info');
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.token
                    },
                    body: JSON.stringify({
                        list: emails
                    })
                });
                
                console.log('批量创建响应状态:', response.status, response.statusText);
                
                const data = await response.json();
                console.log('批量创建响应数据:', JSON.stringify(data, null, 2));
                
                if (response.ok && data.code === 200) {
                    const emailList = emails.map(e => `${e.email} (密码: ${e.password})`).join('\n');
                    showResult('batchResult', `✓ 批量创建成功！\n\n创建的邮箱:\n${emailList}\n\nAPI 响应:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('batchResult', `✗ 批量创建失败\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                console.error('批量创建异常:', error);
                showResult('batchResult', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 5. 测试网络连接
        async function testNetworkConnectivity() {
            console.log('开始测试网络连接');
            showResult('networkResult', '正在测试网络连接...', 'info');
            
            try {
                // 测试基本连接
                const response = await fetch(`${API_CONFIG.baseUrl}`, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                console.log('网络连接测试响应:', response.status, response.statusText);
                
                showResult('networkResult', `✓ 网络连接正常\n服务器响应: ${response.status} ${response.statusText}`, 'success');
            } catch (error) {
                console.error('网络连接测试失败:', error);
                showResult('networkResult', `✗ 网络连接失败: ${error.message}\n\n可能的原因:\n1. 服务器不可达\n2. CORS 配置问题\n3. 网络防火墙阻止`, 'error');
            }
        }

        // 6. 测试 CORS
        async function testCORS() {
            console.log('开始测试 CORS 设置');
            showResult('networkResult', '正在测试 CORS 设置...', 'info');
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/genToken`, {
                    method: 'OPTIONS'
                });
                
                console.log('CORS 预检请求响应:', response.status);
                console.log('CORS 响应头:', [...response.headers.entries()]);
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                showResult('networkResult', `✓ CORS 预检成功\n\nCORS 头信息:\n${JSON.stringify(corsHeaders, null, 2)}`, 'success');
            } catch (error) {
                console.error('CORS 测试失败:', error);
                showResult('networkResult', `✗ CORS 测试失败: ${error.message}\n\n这可能表示服务器不支持 CORS 或配置有误`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('调试页面加载完成');
            console.log('API 配置:', API_CONFIG);
        });
    </script>
</body>
</html>
