// API 配置
const API_CONFIG = {
    baseUrl: 'https://eoceshi.552500.xy',
    token: null, // 将动态生成
    emailDomain: 'ai999.dpdns.org',
    defaultPassword: 'Ai12345@',
    // 管理员账户信息 - 用于生成 Token
    adminEmail: '', // 需要填入管理员邮箱
    adminPassword: '' // 需要填入管理员密码
};

// 全局变量
let isGenerating = false;
let currentResults = {
    success: [],
    failed: [],
    all: []
};

// DOM 元素
const elements = {
    generateBtn: document.getElementById('generateBtn'),
    adminEmail: document.getElementById('adminEmail'),
    adminPassword: document.getElementById('adminPassword'),
    prefix: document.getElementById('prefix'),
    startNumber: document.getElementById('startNumber'),
    count: document.getElementById('count'),
    roleName: document.getElementById('roleName'),
    statusText: document.getElementById('statusText'),
    progressFill: document.getElementById('progressFill'),
    successCount: document.getElementById('successCount'),
    failCount: document.getElementById('failCount'),
    totalCount: document.getElementById('totalCount'),
    successList: document.getElementById('successList'),
    failedList: document.getElementById('failedList'),
    allList: document.getElementById('allList')
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    updateTabDisplay();
});

// 事件监听器
function initializeEventListeners() {
    elements.generateBtn.addEventListener('click', handleGenerate);
    
    // 标签页切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            switchTab(e.target.dataset.tab);
        });
    });
    
    // 导出和复制按钮
    document.getElementById('exportSuccess').addEventListener('click', () => exportData('success'));
    document.getElementById('exportFailed').addEventListener('click', () => exportData('failed'));
    document.getElementById('exportAll').addEventListener('click', () => exportData('all'));
    
    document.getElementById('copySuccess').addEventListener('click', () => copyData('success'));
    document.getElementById('copyFailed').addEventListener('click', () => copyData('failed'));
    document.getElementById('copyAll').addEventListener('click', () => copyData('all'));
}

// 处理生成按钮点击
async function handleGenerate() {
    if (isGenerating) return;

    const adminEmail = elements.adminEmail.value.trim();
    const adminPassword = elements.adminPassword.value.trim();
    const prefix = elements.prefix.value.trim();
    const startNumber = parseInt(elements.startNumber.value);
    const count = parseInt(elements.count.value);
    const roleName = elements.roleName.value.trim();

    // 验证输入
    if (!adminEmail) {
        alert('请输入管理员邮箱');
        return;
    }

    if (!adminPassword) {
        alert('请输入管理员密码');
        return;
    }

    if (!prefix) {
        alert('请输入邮箱前缀');
        return;
    }

    if (count < 1 || count > 100) {
        alert('生成数量必须在 1-100 之间');
        return;
    }

    // 更新 API 配置中的管理员信息
    API_CONFIG.adminEmail = adminEmail;
    API_CONFIG.adminPassword = adminPassword;
    
    // 重置结果
    resetResults();
    
    // 开始生成
    isGenerating = true;
    elements.generateBtn.disabled = true;
    elements.generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    
    try {
        await generateEmails(prefix, startNumber, count, roleName);
    } catch (error) {
        console.error('生成过程出错:', error);
        updateStatus('生成过程出现错误');
    } finally {
        isGenerating = false;
        elements.generateBtn.disabled = false;
        elements.generateBtn.innerHTML = '<i class="fas fa-magic"></i> 开始生成';
    }
}

// 生成邮箱
async function generateEmails(prefix, startNumber, count, roleName) {
    // 首先生成新的 Token
    updateStatus('正在生成访问令牌...');
    try {
        const tokenResult = await generateToken();
        if (!tokenResult.success) {
            throw new Error(tokenResult.error);
        }
        updateStatus(`令牌生成成功: ${tokenResult.token.substring(0, 8)}...`);
    } catch (error) {
        updateStatus(`令牌生成失败: ${error.message}`);
        throw error;
    }

    const emails = [];

    // 生成邮箱列表
    for (let i = 0; i < count; i++) {
        const email = `${prefix}${startNumber + i}@${API_CONFIG.emailDomain}`;
        const emailData = {
            email: email,
            password: API_CONFIG.defaultPassword
        };

        if (roleName) {
            emailData.roleName = roleName;
        }

        emails.push(emailData);
    }

    updateStatus(`准备生成 ${count} 个邮箱...`);
    updateProgress(0);
    
    // 批量发送请求
    for (let i = 0; i < emails.length; i++) {
        const email = emails[i];
        const progress = ((i + 1) / emails.length) * 100;
        
        updateStatus(`正在生成: ${email.email} (${i + 1}/${emails.length})`);
        updateProgress(progress);
        
        try {
            const result = await createUser([email]);

            if (result.success) {
                currentResults.success.push(email.email);
                currentResults.all.push(`✓ ${email.email} - 成功`);
                console.log(`邮箱创建成功: ${email.email}`);
            } else {
                const errorMsg = `${email.email} - ${result.error}`;
                currentResults.failed.push(errorMsg);
                currentResults.all.push(`✗ ${errorMsg}`);
                console.error(`邮箱创建失败: ${errorMsg}`);
            }
        } catch (error) {
            const errorMsg = `${email.email} - 网络错误: ${error.message}`;
            currentResults.failed.push(errorMsg);
            currentResults.all.push(`✗ ${errorMsg}`);
            console.error(`邮箱创建异常: ${errorMsg}`, error);
        }
        
        updateCounts();
        updateResultDisplay();
        
        // 添加小延迟避免请求过快
        if (i < emails.length - 1) {
            await sleep(200);
        }
    }
    
    updateStatus(`生成完成! 成功: ${currentResults.success.length}, 失败: ${currentResults.failed.length}`);
}

// 生成新的 Token
async function generateToken() {
    if (!API_CONFIG.adminEmail || !API_CONFIG.adminPassword) {
        throw new Error('管理员邮箱和密码未配置');
    }

    console.log('开始生成 Token，管理员邮箱:', API_CONFIG.adminEmail);

    try {
        const requestBody = {
            email: API_CONFIG.adminEmail,
            password: API_CONFIG.adminPassword
        };

        console.log('Token 请求参数:', requestBody);

        const response = await fetch(`${API_CONFIG.baseUrl}/api/public/genToken`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('Token 响应状态:', response.status, response.statusText);

        const data = await response.json();
        console.log('Token 响应数据:', data);

        if (response.ok && data.code === 200) {
            API_CONFIG.token = data.data.token;
            console.log('Token 生成成功:', data.data.token);
            return { success: true, token: data.data.token };
        } else {
            const errorMsg = `Token 生成失败 - 状态码: ${response.status}, 错误: ${data.message || '未知错误'}, 完整响应: ${JSON.stringify(data)}`;
            console.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    } catch (error) {
        const errorMsg = `生成 Token 请求失败: ${error.message}`;
        console.error(errorMsg, error);
        throw new Error(errorMsg);
    }
}

// 调用 API 创建用户
async function createUser(userList) {
    if (!API_CONFIG.token) {
        throw new Error('Token 未设置，请先生成 Token');
    }

    console.log('创建用户请求，用户列表:', userList);
    console.log('使用 Token:', API_CONFIG.token.substring(0, 8) + '...');

    try {
        const requestBody = {
            list: userList
        };

        const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': API_CONFIG.token
            },
            body: JSON.stringify(requestBody)
        });

        console.log('创建用户响应状态:', response.status, response.statusText);

        const data = await response.json();
        console.log('创建用户响应数据:', data);

        if (response.ok && data.code === 200) {
            return { success: true, data: data };
        } else {
            const errorMsg = `创建用户失败 - 状态码: ${response.status}, 错误: ${data.message || '未知错误'}, 完整响应: ${JSON.stringify(data)}`;
            console.error(errorMsg);
            return { success: false, error: errorMsg };
        }
    } catch (error) {
        const errorMsg = `创建用户请求失败: ${error.message}`;
        console.error(errorMsg, error);
        throw new Error(errorMsg);
    }
}

// 辅助函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function resetResults() {
    currentResults = { success: [], failed: [], all: [] };
    updateCounts();
    updateResultDisplay();
    updateProgress(0);
}

function updateStatus(text) {
    elements.statusText.textContent = text;
}

function updateProgress(percent) {
    elements.progressFill.style.width = `${percent}%`;
}

function updateCounts() {
    elements.successCount.textContent = currentResults.success.length;
    elements.failCount.textContent = currentResults.failed.length;
    elements.totalCount.textContent = currentResults.success.length + currentResults.failed.length;
}

function updateResultDisplay() {
    elements.successList.innerHTML = currentResults.success.map(email => 
        `<div class="result-item success">${email}</div>`
    ).join('');
    
    elements.failedList.innerHTML = currentResults.failed.map(item => 
        `<div class="result-item error">${item}</div>`
    ).join('');
    
    elements.allList.innerHTML = currentResults.all.map(item => 
        `<div class="result-item">${item}</div>`
    ).join('');
}

function switchTab(tabName) {
    // 更新标签按钮
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // 更新标签面板
    document.querySelectorAll('.tab-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    document.getElementById(`${tabName}Tab`).classList.add('active');
}

function updateTabDisplay() {
    switchTab('success');
}

// 导出数据
function exportData(type) {
    let data = [];
    let filename = '';
    
    switch(type) {
        case 'success':
            data = currentResults.success;
            filename = 'successful_emails.txt';
            break;
        case 'failed':
            data = currentResults.failed;
            filename = 'failed_emails.txt';
            break;
        case 'all':
            data = currentResults.all;
            filename = 'all_results.txt';
            break;
    }
    
    if (data.length === 0) {
        alert('没有数据可导出');
        return;
    }
    
    const content = data.join('\n');
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 复制数据
function copyData(type) {
    let data = [];
    
    switch(type) {
        case 'success':
            data = currentResults.success;
            break;
        case 'failed':
            data = currentResults.failed;
            break;
        case 'all':
            data = currentResults.all;
            break;
    }
    
    if (data.length === 0) {
        alert('没有数据可复制');
        return;
    }
    
    const content = data.join('\n');
    
    navigator.clipboard.writeText(content).then(() => {
        alert('已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动选择文本复制');
    });
}
