<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 邮箱批量生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>邮箱批量生成器 - 功能测试</h1>
    
    <div class="test-section">
        <h2>管理员配置</h2>
        <p>配置管理员账户用于生成访问令牌</p>
        <input type="email" id="adminEmail" placeholder="管理员邮箱" style="width: 200px; margin: 5px;">
        <input type="password" id="adminPassword" placeholder="管理员密码" style="width: 200px; margin: 5px;">
        <button class="test-button" onclick="testTokenGeneration()">生成 Token</button>
        <div id="tokenResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h2>API 连接测试</h2>
        <p>测试与后端 API 的连接状态（需要先生成 Token）</p>
        <button class="test-button" onclick="testAPIConnection()">测试 API 连接</button>
        <div id="apiResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>单个邮箱创建测试</h2>
        <p>测试创建单个邮箱账户</p>
        <input type="text" id="testEmail" placeholder="输入测试邮箱前缀" value="test">
        <button class="test-button" onclick="testSingleEmail()">创建测试邮箱</button>
        <div id="singleResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>批量创建测试</h2>
        <p>测试批量创建邮箱（小批量）</p>
        <input type="text" id="batchPrefix" placeholder="批量前缀" value="batch">
        <input type="number" id="batchCount" value="3" min="1" max="5">
        <button class="test-button" onclick="testBatchEmails()">批量创建测试</button>
        <div id="batchResult" class="test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>项目信息</h2>
        <p><strong>API 地址:</strong> https://eoceshi.552500.xy</p>
        <p><strong>邮箱域名:</strong> ai999.dpdns.org</p>
        <p><strong>默认密码:</strong> Ai12345@</p>
        <p><strong>Token 状态:</strong> <span id="tokenStatus">未生成</span></p>
        <p><strong>当前 Token:</strong> <span id="currentToken">无</span></p>
    </div>

    <script>
        // API 配置
        const API_CONFIG = {
            baseUrl: 'https://eoceshi.552500.xy',
            token: null, // 将动态生成
            emailDomain: 'ai999.dpdns.org',
            defaultPassword: 'Ai12345@'
        };

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `test-result ${type}`;
            element.innerHTML = message;
        }

        // 测试 Token 生成
        async function testTokenGeneration() {
            const adminEmail = document.getElementById('adminEmail').value.trim();
            const adminPassword = document.getElementById('adminPassword').value.trim();

            if (!adminEmail || !adminPassword) {
                showResult('tokenResult', '请输入管理员邮箱和密码', 'error');
                return;
            }

            showResult('tokenResult', '正在生成 Token...', 'info');

            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/genToken`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: adminEmail,
                        password: adminPassword
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 200) {
                    API_CONFIG.token = data.data.token;
                    document.getElementById('tokenStatus').textContent = '已生成';
                    document.getElementById('currentToken').textContent = data.data.token.substring(0, 16) + '...';
                    showResult('tokenResult', `✓ Token 生成成功！\nToken: ${data.data.token}\n\n完整响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    document.getElementById('tokenStatus').textContent = '生成失败';
                    document.getElementById('currentToken').textContent = '无';
                    showResult('tokenResult', `✗ Token 生成失败\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}\n完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('tokenResult', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 测试 API 连接
        async function testAPIConnection() {
            if (!API_CONFIG.token) {
                showResult('apiResult', '请先生成 Token', 'error');
                return;
            }

            showResult('apiResult', '正在测试连接...', 'info');

            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.token
                    },
                    body: JSON.stringify({
                        list: []
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showResult('apiResult', `✓ API 连接成功！\n使用 Token: ${API_CONFIG.token.substring(0, 8)}...\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('apiResult', `⚠ API 响应异常\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('apiResult', `✗ 连接失败: ${error.message}`, 'error');
            }
        }

        // 测试单个邮箱创建
        async function testSingleEmail() {
            if (!API_CONFIG.token) {
                showResult('singleResult', '请先生成 Token', 'error');
                return;
            }

            const prefix = document.getElementById('testEmail').value.trim();
            if (!prefix) {
                showResult('singleResult', '请输入邮箱前缀', 'error');
                return;
            }
            
            const email = `${prefix}_${Date.now()}@${API_CONFIG.emailDomain}`;
            showResult('singleResult', `正在创建: ${email}`, 'info');
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.token
                    },
                    body: JSON.stringify({
                        list: [{
                            email: email,
                            password: API_CONFIG.defaultPassword
                        }]
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showResult('singleResult', `✓ 邮箱创建成功！\n邮箱: ${email}\n密码: ${API_CONFIG.defaultPassword}\n响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('singleResult', `✗ 创建失败\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}\n完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('singleResult', `✗ 请求失败: ${error.message}`, 'error');
            }
        }

        // 测试批量创建
        async function testBatchEmails() {
            if (!API_CONFIG.token) {
                showResult('batchResult', '请先生成 Token', 'error');
                return;
            }

            const prefix = document.getElementById('batchPrefix').value.trim();
            const count = parseInt(document.getElementById('batchCount').value);

            if (!prefix) {
                showResult('batchResult', '请输入批量前缀', 'error');
                return;
            }
            
            if (count < 1 || count > 5) {
                showResult('batchResult', '测试数量应在 1-5 之间', 'error');
                return;
            }
            
            const timestamp = Date.now();
            const emails = [];
            
            for (let i = 1; i <= count; i++) {
                emails.push({
                    email: `${prefix}${i}_${timestamp}@${API_CONFIG.emailDomain}`,
                    password: API_CONFIG.defaultPassword
                });
            }
            
            showResult('batchResult', `正在批量创建 ${count} 个邮箱...`, 'info');
            
            try {
                const response = await fetch(`${API_CONFIG.baseUrl}/api/public/addUser`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': API_CONFIG.token
                    },
                    body: JSON.stringify({
                        list: emails
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    const emailList = emails.map(e => `${e.email} (密码: ${e.password})`).join('\n');
                    showResult('batchResult', `✓ 批量创建成功！\n\n创建的邮箱:\n${emailList}\n\nAPI 响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult('batchResult', `✗ 批量创建失败\n状态码: ${response.status}\n错误: ${data.message || '未知错误'}\n完整响应: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult('batchResult', `✗ 请求失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
