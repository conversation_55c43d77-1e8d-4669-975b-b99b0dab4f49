# 快速启动指南

## 🚀 立即部署到 Cloudflare Pages

### 方法一：一键部署（推荐）

1. **Fork 或下载项目**
   - 将项目文件下载到本地
   - 或者 Fork 这个仓库到你的 GitHub

2. **上传到 Git 仓库**
   ```bash
   git init
   git add .
   git commit -m "邮箱批量生成器初始版本"
   git remote add origin <你的仓库地址>
   git push -u origin main
   ```

3. **连接到 Cloudflare Pages**
   - 访问 [Cloudflare Pages](https://pages.cloudflare.com/)
   - 点击 "创建项目" → "连接到 Git"
   - 选择你的仓库
   - 构建设置保持默认（留空）
   - 点击 "保存并部署"

4. **等待部署完成**
   - 通常 1-3 分钟完成
   - 获得 `*.pages.dev` 域名

### 方法二：直接上传

1. **打包文件**
   - 选择以下文件打包成 ZIP：
     - `index.html`
     - `style.css` 
     - `script.js`
     - `_headers`

2. **上传部署**
   - 在 Cloudflare Pages 选择 "上传资产"
   - 上传 ZIP 文件
   - 点击 "部署站点"

## 🧪 本地测试

### 使用 Python（推荐）
```bash
# Python 3
python -m http.server 8000

# 访问 http://localhost:8000
```

### 使用 Node.js
```bash
npx serve .
# 或
npx http-server
```

### 功能测试
打开 `test.html` 进行 API 连接和功能测试

## ⚙️ 配置说明

### API 配置
在 `script.js` 中的 `API_CONFIG` 对象：

```javascript
const API_CONFIG = {
    baseUrl: 'https://eoceshi.552500.xy',    // API 服务器地址
    token: 'apiceshi******',                  // 认证令牌
    emailDomain: 'ai999.dpdns.org',          // 邮箱域名
    defaultPassword: 'Ai12345@'              // 默认密码
};
```

### 安全配置
`_headers` 文件包含了安全头配置，确保：
- XSS 保护
- 内容类型保护
- CSP 策略
- 允许的 API 域名

## 📱 使用说明

1. **设置参数**
   - 邮箱前缀：如 "user"
   - 起始编号：如 1
   - 生成数量：1-100
   - 权限身份：可选

2. **开始生成**
   - 点击 "开始生成" 按钮
   - 实时查看进度
   - 查看成功/失败统计

3. **导出结果**
   - 成功列表
   - 失败列表  
   - 完整日志
   - 支持复制和下载

## 🔧 自定义配置

### 修改 API 配置
如需更改 API 设置，编辑 `script.js` 第 2-7 行

### 修改样式
编辑 `style.css` 自定义界面外观

### 修改功能
编辑 `script.js` 添加新功能或修改逻辑

## 🌐 域名绑定

### 自定义域名
1. 在 Cloudflare Pages 项目设置中
2. 点击 "自定义域名"
3. 添加你的域名
4. 配置 DNS 记录（CNAME 指向 `*.pages.dev`）

### SSL 证书
Cloudflare 自动提供免费 SSL 证书

## 📊 监控和分析

### Web Analytics
在项目设置中启用 Cloudflare Web Analytics

### 错误监控
查看浏览器开发者工具的控制台

## 🆘 故障排除

### 常见问题

1. **CORS 错误**
   - 检查 API 服务器 CORS 设置
   - 确认 `_headers` 文件配置

2. **API 调用失败**
   - 验证 API 地址和令牌
   - 使用 `test.html` 测试连接

3. **页面无法访问**
   - 检查 DNS 配置
   - 确认部署状态

### 调试步骤
1. 打开浏览器开发者工具
2. 查看控制台错误信息
3. 检查网络请求状态
4. 使用测试页面验证功能

## 📞 技术支持

如遇问题，请检查：
1. `README.md` - 详细说明
2. `DEPLOYMENT.md` - 部署指南
3. `test.html` - 功能测试
4. 浏览器开发者工具

---

🎉 **恭喜！你的邮箱批量生成器已准备就绪！**
